"use client";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card as CardType } from "@/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import { motion, useMotionValue, useTransform } from "framer-motion";
import { 
  Calendar, 
  DollarSign, 
  User, 
  Building2, 
  GripVertical,
  MoreHorizontal,
  Trash2,
  Edit,
  AlertTriangle,
  ArrowUp,
  Minus,
  ArrowDown,
  Clock,
  TrendingUp,
  Sparkles
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

interface EnhancedCardItemProps {
  card: CardType;
  onEditCard?: (card: CardType) => void;
  onDeleteCard?: (cardId: string) => void;
  onCardClick?: (card: CardType) => void;
  isDragOverlay?: boolean;
}

export function EnhancedCardItem({
  card,
  onEditCard,
  onDeleteCard,
  onCardClick,
  isDragOverlay = false
}: EnhancedCardItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: card.id,
    data: {
      card,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Motion values for magnetic effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useTransform(y, [-100, 100], [30, -30]);
  const rotateY = useTransform(x, [-100, 100], [-30, 30]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'from-red-500 to-red-600';
      case 'high': return 'from-orange-500 to-orange-600';
      case 'medium': return 'from-yellow-500 to-yellow-600';
      case 'low': return 'from-gray-400 to-gray-500';
      default: return 'from-blue-500 to-blue-600';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertTriangle className="h-3 w-3" />;
      case 'high': return <ArrowUp className="h-3 w-3" />;
      case 'medium': return <Minus className="h-3 w-3" />;
      case 'low': return <ArrowDown className="h-3 w-3" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700';
      case 'contacted': return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700';
      case 'qualified': return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700';
      case 'proposal': return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700';
      case 'negotiation': return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700';
      case 'closed_won': return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700';
      case 'closed_lost': return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700';
      default: return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700';
    }
  };

  if (isDragOverlay) {
    return (
      <motion.div
        initial={{ scale: 1, rotate: 0 }}
        animate={{ scale: 1.05, rotate: 3 }}
        className="relative"
      >
        {/* Glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-xl blur-xl scale-110" />
        
        <Card className="relative overflow-hidden backdrop-blur-xl bg-card/95 border-2 border-primary/30 shadow-2xl shadow-primary/20">
          {/* Content similar to regular card but with enhanced effects */}
          <CardContent className="p-4">
            {/* Simplified content for drag overlay */}
            <h3 className="font-semibold text-lg mb-2 line-clamp-2">
              {card.title}
            </h3>
            {card.value && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <span className="font-semibold text-green-600">
                  {formatCurrency(card.value)}
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger click if it's a drag operation or dropdown action
    if (isDragging) return;

    // Check if click is on dropdown or action buttons
    const target = e.target as HTMLElement;
    if (target.closest('[role="menuitem"]') || target.closest('button')) {
      return;
    }

    onCardClick?.(card);
  };

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      whileHover={{
        y: -4,
        scale: 1.02,
        transition: { type: "spring", stiffness: 300, damping: 20 }
      }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => {
        // Add subtle haptic feedback if available
        if ('vibrate' in navigator) {
          navigator.vibrate(10);
        }
      }}
      onClick={handleCardClick}
      className={cn(
        "group relative cursor-grab active:cursor-grabbing",
        isDragging && "opacity-50 scale-105 rotate-3 z-50 cursor-grabbing"
      )}
      {...attributes}
      {...listeners}
    >
      {/* Hover glow effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        initial={{ scale: 0.8 }}
        whileHover={{ scale: 1.1 }}
      />

      <Card className={cn(
        "relative overflow-hidden backdrop-blur-sm bg-card/90 border border-border/50",
        "transition-all duration-300 ease-out",
        "hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30",
        "group-hover:bg-card/95",
        isDragging && "shadow-2xl shadow-primary/20 border-primary/50"
      )}>
        {/* Priority Accent with shimmer effect */}
        {card.priority && (
          <motion.div 
            className={cn(
              "absolute top-0 left-0 w-full h-1 bg-gradient-to-r",
              getPriorityColor(card.priority)
            )}
            animate={{ 
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity, 
              ease: "linear" 
            }}
            style={{
              backgroundSize: "200% 100%"
            }}
          />
        )}

        <CardContent className="p-4">
          {/* Drag indicator */}
          <motion.div
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-50 transition-opacity duration-200"
            whileHover={{ scale: 1.2, opacity: 0.8 }}
          >
            <GripVertical className="h-3 w-3 text-muted-foreground" />
          </motion.div>

          {/* Actions */}
          <div className="flex items-center justify-between mb-3">
            {card.priority && (
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs border-0 text-white font-medium",
                    "bg-gradient-to-r", getPriorityColor(card.priority)
                  )}
                >
                  {getPriorityIcon(card.priority)}
                  <span className="ml-1 capitalize">{card.priority}</span>
                </Badge>
              </motion.div>
            )}

            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className={cn(
                        "h-7 w-7 p-0 opacity-0 group-hover:opacity-100",
                        "transition-all duration-200 hover:bg-muted/80"
                      )}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="backdrop-blur-sm">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditCard?.(card);
                    }}
                    className="cursor-pointer"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Редактировать
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteCard?.(card.id);
                    }}
                    className="cursor-pointer text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Удалить
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Card Title with gradient text effect */}
          <motion.h3 
            className="font-semibold text-lg mb-2 line-clamp-2 text-foreground group-hover:text-primary transition-colors duration-300"
            whileHover={{ scale: 1.02 }}
          >
            {card.title}
          </motion.h3>

          {/* Card Description */}
          {card.description && (
            <p className="text-sm text-muted-foreground mb-3 line-clamp-2 group-hover:text-muted-foreground/80 transition-colors">
              {card.description}
            </p>
          )}

          {/* Value with animated counter effect */}
          {card.value && (
            <motion.div 
              className="flex items-center gap-2 mb-3"
              whileHover={{ scale: 1.05 }}
            >
              <motion.div 
                className="p-1.5 rounded-full bg-green-100 dark:bg-green-900/30"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <DollarSign className="h-3 w-3 text-green-600 dark:text-green-400" />
              </motion.div>
              <span className="font-semibold text-green-600 dark:text-green-400">
                {formatCurrency(card.value)}
              </span>
            </motion.div>
          )}

          {/* Contact Info */}
          {(card.contact_name || card.company_name) && (
            <div className="space-y-2 mb-3">
              {card.contact_name && (
                <motion.div 
                  className="flex items-center gap-2 text-sm"
                  whileHover={{ x: 4 }}
                >
                  <User className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{card.contact_name}</span>
                </motion.div>
              )}
              {card.company_name && (
                <motion.div 
                  className="flex items-center gap-2 text-sm"
                  whileHover={{ x: 4 }}
                >
                  <Building2 className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{card.company_name}</span>
                </motion.div>
              )}
            </div>
          )}

          {/* Dates */}
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
            {card.expected_close_date && (
              <motion.div 
                className="flex items-center gap-1"
                whileHover={{ scale: 1.05 }}
              >
                <Calendar className="h-3 w-3" />
                <span>{formatDate(card.expected_close_date)}</span>
              </motion.div>
            )}
            {card.created_at && (
              <motion.div 
                className="flex items-center gap-1"
                whileHover={{ scale: 1.05 }}
              >
                <Clock className="h-3 w-3" />
                <span>{formatDate(card.created_at)}</span>
              </motion.div>
            )}
          </div>

          {/* Status Badge */}
          {card.status && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", getStatusColor(card.status))}
                >
                  {card.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </motion.div>
            </div>
          )}
        </CardContent>

        {/* Animated border on hover */}
        <motion.div
          className="absolute inset-0 rounded-xl border-2 border-primary/0 group-hover:border-primary/20 transition-all duration-300"
          whileHover={{
            borderColor: "rgba(var(--primary), 0.3)"
          }}
        />
      </Card>
    </motion.div>
  );
}
