"use client";

import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { EnhancedCardItem } from "./enhanced-card-item";
import { ModernDropZone } from "./modern-drop-zone";
import { Stage, Card as CardType } from "@/types";
import { formatCurrency } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { TrendingUp } from "lucide-react";

interface StageColumnProps {
  stage: Stage;
  cards: CardType[];
  onEditCard?: (card: CardType) => void;
  onDeleteCard?: (cardId: string) => void;
  onAddCard?: (stageId: string) => void;
  onCardClick?: (card: CardType) => void;
}

export function StageColumn({ stage, cards, onEditCard, onDeleteCard, onAddCard, onCardClick }: StageColumnProps) {
  const totalValue = cards.reduce((sum, card) => sum + (card.value || 0), 0);

  return (
    <motion.div 
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex-shrink-0 w-80"
    >
      <Card className="h-full bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full shadow-sm"
                style={{ backgroundColor: stage.color || "#6366f1" }}
              />
              <span className="text-foreground">{stage.name}</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary" 
                className="text-xs bg-muted/50 hover:bg-muted transition-colors"
              >
                {cards.length}
              </Badge>
            </div>
          </div>
          {totalValue > 0 && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center gap-1 mt-2"
            >
              <TrendingUp className="h-3 w-3 text-green-600" />
              <p className="text-sm font-medium text-green-600">
                {formatCurrency(totalValue)}
              </p>
            </motion.div>
          )}
        </CardHeader>

        <CardContent className="pt-0">
          <ModernDropZone
            id={stage.id}
            isEmpty={cards.length === 0}
            onAddCard={() => onAddCard?.(stage.id)}
          >
            <SortableContext
              items={cards.map((card) => card.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-3">
                <AnimatePresence mode="popLayout">
                  {cards.map((card) => (
                    <motion.div
                      key={card.id}
                      layout
                      initial={{ opacity: 0, y: 20, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -20, scale: 0.9 }}
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 30,
                        opacity: { duration: 0.2 }
                      }}
                    >
                      <EnhancedCardItem
                        card={card}
                        onEditCard={onEditCard}
                        onDeleteCard={onDeleteCard}
                        onCardClick={onCardClick}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </SortableContext>
          </ModernDropZone>
        </CardContent>
      </Card>
    </motion.div>
  );
}